<script lang="ts">
	import Filters from '$lib/nel/Filters.svelte';
	import { filterData, type FilterCriteria } from '$lib/nel/filters/filterData';
	import { buildTreeFromTable } from '$lib/nel/treeDataSet';
	import { getData } from './data.remote';
	import MyTreeView from '../lib/generic/tree/custom/MyTreeView.svelte';
	import { TreeItemViewAdvanced } from '$lib/generic/tree/advanced/TreeItemViewSnippet.svelte';

	let dataSet = await getData();

	let tree = buildTreeFromTable(dataSet);
	let treeState = $state(tree);

	function onFilterChanged(filterCriteria: FilterCriteria) {
		filterData(treeState, filterCriteria);
	}

	let compact = $state(false);
</script>

<div class="p-2">
	<label class="inline-flex cursor-pointer items-center">
		<input type="checkbox" bind:checked={compact} class="peer sr-only" />
		<div
			class="peer relative h-6 w-11 rounded-full bg-gray-200 peer-checked:bg-blue-600 peer-focus:ring-4 peer-focus:ring-blue-300 peer-focus:outline-none after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white rtl:peer-checked:after:-translate-x-full dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-blue-600 dark:peer-focus:ring-blue-800"
		></div>
		<span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Compact</span>
	</label>
</div>

<div class="flex flex-1 gap-4 overflow-hidden">
	<div class="w-2/3 flex-1 overflow-y-auto">
		<MyTreeView
			treeData={treeState}
			treeItemView={TreeItemViewAdvanced}
			--var-padding={compact ? 0 : '0.5rem'}
		/>
	</div>
	<div class="flex-1 overflow-y-auto">
		<Filters data={dataSet} onSelection={onFilterChanged} />
	</div>
</div>
