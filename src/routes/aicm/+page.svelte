<script lang="ts">
	import { TreeItemViewDefault } from '$lib/generic/tree/extended/TreeItemViewSnippet.svelte';
	import MyTreeView from '../../lib/generic/tree/custom/MyTreeView.svelte';
	import { getData } from './data.remote';
	import { buildMyTreeFromTable } from './treeDataSet';

	const dataSet = await getData();

	const tree = buildMyTreeFromTable(dataSet);
	let treeState = $state(tree);

	let compact = $state(false);
</script>

<p class="text-center text-xl">Asset Classification</p>

<div class="flex flex-1 gap-4 overflow-hidden">
	<div class="w-2/3 flex-1 overflow-y-auto">
		<MyTreeView
			treeData={treeState}
			treeItemView={TreeItemViewDefault}
			--var-padding={compact ? 0 : '0.5rem'}
		/>
	</div>
	<div class="flex-1 overflow-y-auto">
		<!-- <Filters data={dataSet} onSelection={onFilterChanged} /> -->
	</div>
</div>
